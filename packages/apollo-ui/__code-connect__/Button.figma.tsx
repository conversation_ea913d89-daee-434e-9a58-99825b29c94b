import React from "react"
import figma from "@figma/code-connect"

import { Button } from "../src/components/button/Button"

figma.connect(
  Button,
  "https://www.figma.com/design/5ndGGLsLILgxao32M6rJ0F/Apollo-Design-System?node-id=1356%3A1119",
  {
    imports: ["import { Button } from '@apollo/ui'"],
    props: {
      disabled: figma.enum("State", {
        disabled: true,
      }),
      startDecorator: figma.enum("IconPosition", {
        left: figma.instance("Icon"),
        right: undefined,
      }),
      endDecorator: figma.enum("IconPosition", {
        left: undefined,
        right: figma.instance("Icon"),
      }),
      variant: figma.enum("Type", {
        Filled: "filled",
        outline: "outline",
        Plain: "plain",
      }),
      size: figma.enum("Size", {
        Small: "small",
        Medium: "medium",
      }),
      color: figma.enum("Style", {
        General: "primary",
        Negativity: "negative",
      }),
      text: figma.string("Text"),
    },
    example: (props) => (
      <Button
        variant={props.variant}
        size={props.size}
        color={props.color}
        disabled={props.disabled}
        startDecorator={props.startDecorator}
        endDecorator={props.endDecorator}
      >
        {props.text}
      </Button>
    ),
  }
)
