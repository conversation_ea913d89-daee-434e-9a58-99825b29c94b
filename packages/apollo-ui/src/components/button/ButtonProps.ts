import type {
  AnchorHTMLAttributes,
  ButtonHTMLAttributes,
  ReactNode,
  Ref,
} from "react"

export type BaseButtonProps = {
  fullWidth?: boolean
  /** @deprecated "solid" is renamed to "filled". Both are accepted temporarily. */
  variant?: "filled" | "outline" | "plain" | "solid"
  size?: "medium" | "small"
  /** @deprecated "danger" is renamed to "negative". Both are accepted temporarily. */
  color?: "primary" | "negative" | "danger"
  startDecorator?: ReactNode
  endDecorator?: ReactNode
  ref?: Ref<HTMLButtonElement>
}

export type LinkButtonProps = {
  href: string
  fileType?: string
} & Omit<AnchorHTMLAttributes<HTMLAnchorElement>, "type">

export type NormalButtonProps = ButtonHTMLAttributes<HTMLButtonElement>

export type ButtonProps = (
  | ({ href: string } & LinkButtonProps)
  | ({
      href?: never
    } & NormalButtonProps)
) &
  BaseButtonProps
