"use client"

import { forwardRef, useImperativeHandle, useMemo, useRef } from "react"
import type { ButtonRootSlotProps } from "@mui/base/Button"
import { useButton } from "@mui/base/useButton"
import { useSlotProps, type WithOptionalOwnerState } from "@mui/base/utils"
import { unstable_useForkRef as useForkRef } from "@mui/utils"
import { cva } from "class-variance-authority"
import { clsx } from "clsx"

import type { ButtonProps } from "@/components/Button/ButtonProps"
import { mergeClass } from "@/utils/helpers"

export const buttonVariants = cva(
  `inline-flex items-center justify-center whitespace-nowrap rounded-md font-medium transition-colors
  focus-visible:outline-none focus-visible:ring focus-visible:ring-ring focus-visible:ring-offset-0
  active:!bg-surface-static-default3
  disabled:pointer-events-none disabled:text-content-disabled`,
  {
    variants: {
      variant: {
        solid:
          "md:hover:bg-black md:hover:text-white transition-colors disabled:bg-surface-action-primary-disabled disabled:border-border-disabled",
        filled:
          "md:hover:bg-black md:hover:text-white transition-colors disabled:bg-surface-action-primary-disabled disabled:border-border-disabled",
        plain: "bg-black text-white",
        outline:
          "border border-black transition-colors shadow-none disabled:border-border-disabled",
      },
      width: {
        default: "w-auto",
        full: "w-full",
      },
      size: {
        sm: "px-[16px] py-[4px]",
        md: "px-[16px] py-[4px]",
        lg: "px-[16px] py-[8px]",
      },
      rounded: {
        default: "rounded-md",
        sm: "rounded-sm",
        lg: "rounded-lg",
        xl: "rounded-xl",
        xxl: "rounded-2xl",
        none: "rounded-none",
        full: "rounded-full",
      },
      color: {
        primary:
          "bg-surface-action-primary-default text-white dark:text-white focus-visible:ring-border-focus",
        danger:
          "bg-surface-action-delete-default text-white dark:text-white focus-visible:ring-border-focus",
        negative:
          "bg-surface-action-delete-default text-white dark:text-white focus-visible:ring-border-focus",
      },
    },
    compoundVariants: [
      // Danger/Negative combinations
      {
        variant: "solid",
        color: "danger",
        className:
          "active:!bg-surface-action-delete-active active:!border-surface-action-delete-active border border-surface-action-delete-default md:hover:bg-surface-action-delete-hover md:hover:border-surface-action-delete-hover",
      },
      {
        variant: "solid",
        color: "negative",
        className:
          "active:!bg-surface-action-delete-active active:!border-surface-action-delete-active border border-surface-action-delete-default md:hover:bg-surface-action-delete-hover md:hover:border-surface-action-delete-hover",
      },
      {
        variant: "outline",
        color: "danger",
        className:
          "border-surface-action-delete-default text-surface-action-delete-default bg-transparent md:hover:border-border-danger-subdued md:hover:text-border-danger-subdued",
      },
      {
        variant: "outline",
        color: "negative",
        className:
          "border-surface-action-delete-default text-surface-action-delete-default bg-transparent md:hover:border-border-danger-subdued md:hover:text-border-danger-subdued",
      },
      {
        variant: "plain",
        color: "danger",
        className:
          " bg-transparent text-surface-action-delete-default md:hover:bg-gray-10 disabled:border-none",
      },
      {
        variant: "plain",
        color: "negative",
        className:
          " bg-transparent text-surface-action-delete-default md:hover:bg-gray-10 disabled:border-none",
      },
      // Primary combinations
      {
        variant: "solid",
        color: "primary",
        className:
          "border border-surface-action-primary-default md:hover:bg-surface-action-primary-hover md:hover:border-surface-action-primary-hover active:!border-surface-action-primary-active active:!bg-surface-action-primary-active",
      },
      {
        variant: "filled",
        color: "primary",
        className:
          "border border-surface-action-primary-default md:hover:bg-surface-action-primary-hover md:hover:border-surface-action-primary-hover active:!border-surface-action-primary-active active:!bg-surface-action-primary-active",
      },
      {
        variant: "outline",
        color: "primary",
        className:
          "border-surface-action-primary-default text-surface-action-primary-default bg-transparent md:hover:border-border-primary-subdued md:hover:text-border-primary-subdued",
      },
      {
        variant: "plain",
        color: "primary",
        className:
          " bg-transparent text-surface-action-primary-default md:hover:bg-gray-10 disabled:border-none",
      },
      // Filled + Negative combinations
      {
        variant: "filled",
        color: "negative",
        className:
          "active:!bg-surface-action-delete-active active:!border-surface-action-delete-active border border-surface-action-delete-default md:hover:bg-surface-action-delete-hover md:hover:border-surface-action-delete-hover",
      },
    ],
    defaultVariants: {
      variant: "filled",
      color: "primary",
      size: "lg",
      rounded: "default",
      width: "default",
    },
  }
)

const Button = forwardRef<HTMLButtonElement, ButtonProps>(
  function Button(props, ref) {
    const {
      children,
      action,
      color,
      variant,
      size,
      fullWidth = false,
      startDecorator,
      endDecorator,
      loading = false,
      loadingPosition = "start",
      loadingIndicator: loadingIndicatorProp,
      disabled: disabledProp,
      as,
      slotProps = {},
      ...externalForwardedProps
    } = props

    const buttonRef = useRef<HTMLElement>(null)
    const handleRef = useForkRef(buttonRef, ref)
    const disabled = loading || disabledProp

    const { focusVisible, active, setFocusVisible, rootRef, getRootProps } =
      useButton({
        ...props,
        disabled,
        rootRef: handleRef,
      })

    const ownerState = {
      ...props,
      color,
      fullWidth,
      variant,
      size,
      loading,
      loadingPosition,
      disabled,
      focusVisible,
      active,
    }

    useImperativeHandle(
      action,
      () => ({
        focusVisible: () => {
          setFocusVisible(true)
          buttonRef.current!.focus()
        },
      }),
      [setFocusVisible]
    )

    const defaultElement = useMemo(
      () =>
        externalForwardedProps.href || externalForwardedProps.to
          ? "a"
          : "button",
      [externalForwardedProps.href, externalForwardedProps.to]
    )

    const Root: React.ElementType = as ?? defaultElement

    const rootProps: WithOptionalOwnerState<ButtonRootSlotProps> = useSlotProps(
      {
        elementType: Root,
        getSlotProps: getRootProps,
        externalForwardedProps,
        externalSlotProps: slotProps.root,
        additionalProps: { ref: rootRef },
        ownerState,
        className: mergeClass(
          buttonVariants({
            variant,
            size,
            width: fullWidth ? "full" : "default",
            color,
          }),
          externalForwardedProps.className
        ),
      }
    )

    const loadingIndicator = loadingIndicatorProp ?? (
      <svg
        className={clsx("animate-spin p-[2px] h-full w-5 text-black", {
          "mr-2": children && loadingPosition === "start",
          "ml-2": children && loadingPosition === "end",
        })}
        fill="none"
        viewBox="0 0 24 24"
        xmlns="http://www.w3.org/2000/svg"
      >
        <circle
          className="opacity-25"
          cx="12"
          cy="12"
          r="10"
          stroke="currentColor"
          strokeWidth="4"
        />
        <path
          className="opacity-25"
          d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"
          fill="currentColor"
        />
      </svg>
    )

    return (
      <Root {...rootProps} role={rootProps?.role ?? "button"}>
        {startDecorator || (loading && loadingPosition === "start") ? (
          <>
            {loading && loadingPosition === "start"
              ? loadingIndicator
              : startDecorator}
          </>
        ) : null}

        {children}

        {endDecorator || (loading && loadingPosition === "end") ? (
          <> {loading ? loadingIndicator : endDecorator} </>
        ) : null}
      </Root>
    )
  }
)

Button.displayName = "Button"

export default Button
