import {render, screen} from '@testing-library/react'
import Button, { buttonVariants } from "./Button"
import { mergeClass } from '@/utils/helpers'

describe("<Button />", () => {
  test('loads and displays button with primary color (default)', async () => {
    const BtnMsg = "Test Button"
    const BtnOutlineMsg = "Test Button Outline"
    const BtnPlainMsg = "Test Button Plain"
    render(
      <>
        <Button data-testid="test-button-default">{BtnMsg}</Button>
        <Button data-testid="test-button-outline" variant="outline" >{BtnOutlineMsg}</Button>
        <Button data-testid="test-button-plain" variant="plain" >{BtnPlainMsg}</Button>
      </>
    )

    const btn = screen.getByTestId('test-button-default')
    const btnOutline = screen.getByTestId('test-button-outline')
    const btnPlain = screen.getByTestId('test-button-plain')

    expect(btn.textContent).toBe(BtnMsg)
    expect(btnOutline.textContent).toBe(BtnOutlineMsg)
    expect(btnPlain.textContent).toBe(BtnPlainMsg)

    // default (filled)
    expect(btn).toHaveClass(mergeClass(buttonVariants({ })))
    expect(btn).toHaveClass(mergeClass(buttonVariants({ color: "primary" })))
    expect(btn).toHaveClass(mergeClass(buttonVariants({ variant: "filled" })))
    expect(btn).toHaveClass(mergeClass(buttonVariants({ variant: "filled", color: "primary" })))

    // outline
    expect(btnOutline).toHaveClass(mergeClass(buttonVariants({ variant: "outline" })))
    expect(btnOutline).toHaveClass(mergeClass(buttonVariants({ variant: "outline", color: "primary" })))

    // plain
    expect(btnPlain).toHaveClass(mergeClass(buttonVariants({ variant: "plain" })))
    expect(btnPlain).toHaveClass(mergeClass(buttonVariants({ variant: "plain", color: "primary" })))
  })

  test('loads and displays button with negative color', async () => {
    const BtnMsg = "Test Button"
    const BtnOutlineMsg = "Test Button Outline"
    const BtnPlainMsg = "Test Button Plain"
    render(
      <>
        <Button color="negative" data-testid="test-button-default">{BtnMsg}</Button>
        <Button color="negative" data-testid="test-button-outline" variant="outline">{BtnOutlineMsg}</Button>
        <Button color="negative" data-testid="test-button-plain" variant="plain">{BtnPlainMsg}</Button>
      </>
    )

    const btn = screen.getByTestId('test-button-default')
    const btnOutline = screen.getByTestId('test-button-outline')
    const btnPlain = screen.getByTestId('test-button-plain')

    expect(btn.textContent).toBe(BtnMsg)
    expect(btnOutline.textContent).toBe(BtnOutlineMsg)
    expect(btnPlain.textContent).toBe(BtnPlainMsg)

    // default (filled)
    expect(btn).toHaveClass(mergeClass(buttonVariants({ color: "negative" })))
    expect(btn).toHaveClass(mergeClass(buttonVariants({ variant: "filled", color: "negative" })))

    // outline
    expect(btnOutline).toHaveClass(mergeClass(buttonVariants({ variant: "outline", color: "negative" })))

    // plain
    expect(btnPlain).toHaveClass(mergeClass(buttonVariants({ variant: "plain", color: "negative" })))
  })
})
